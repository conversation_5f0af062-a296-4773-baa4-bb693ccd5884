<template>
  <div class="stats-card">
    <div class="stats-header">
      <h3 class="stats-title">
        <el-icon class="title-icon"><DataAnalysis /></el-icon>
        活动统计
      </h3>
    </div>
    
    <div class="stats-grid">
      <div class="stat-item" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :style="{ backgroundColor: stat.color }">
          <el-icon :size="20">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-trend" :class="stat.trend.type">
            <el-icon :size="12">
              <component :is="stat.trend.icon" />
            </el-icon>
            <span>{{ stat.trend.text }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 活动图表 -->
    <div class="activity-chart">
      <h4 class="chart-title">最近活动</h4>
      <div class="chart-container">
        <canvas ref="activityChart" width="300" height="150"></canvas>
      </div>
    </div>

    <!-- 最近操作 -->
    <div class="recent-activities">
      <h4 class="activities-title">最近操作</h4>
      <div class="activities-list">
        <div 
          class="activity-item" 
          v-for="activity in recentActivities" 
          :key="activity.id"
        >
          <div class="activity-icon" :style="{ backgroundColor: activity.color }">
            <el-icon :size="16">
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-text">{{ activity.text }}</div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export default {
  name: 'UserStatsCard',
  data() {
    return {
      stats: [
        {
          key: 'loginCount',
          label: '登录次数',
          value: this.getLoginCount(),
          icon: 'User',
          color: '#409eff',
          trend: {
            type: 'up',
            icon: 'ArrowUp',
            text: '+12%'
          }
        },
        {
          key: 'activeTime',
          label: '在线时长',
          value: this.getActiveTime(),
          icon: 'Timer',
          color: '#67c23a',
          trend: {
            type: 'up',
            icon: 'ArrowUp',
            text: '+5h'
          }
        },
        {
          key: 'dataUsage',
          label: '数据使用',
          value: this.getDataUsage(),
          icon: 'DataBoard',
          color: '#e6a23c',
          trend: {
            type: 'down',
            icon: 'ArrowDown',
            text: '-2.3%'
          }
        },
        {
          key: 'achievements',
          label: '成就数量',
          value: this.getAchievements(),
          icon: 'Trophy',
          color: '#f56c6c',
          trend: {
            type: 'up',
            icon: 'ArrowUp',
            text: '+3'
          }
        }
      ],
      recentActivities: [
        {
          id: 1,
          text: '更新了个人资料',
          time: new Date(Date.now() - 5 * 60 * 1000),
          icon: 'Edit',
          color: '#409eff'
        },
        {
          id: 2,
          text: '修改了密码',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000),
          icon: 'Lock',
          color: '#67c23a'
        },
        {
          id: 3,
          text: '上传了新头像',
          time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          icon: 'Picture',
          color: '#e6a23c'
        },
        {
          id: 4,
          text: '登录系统',
          time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          icon: 'User',
          color: '#909399'
        }
      ]
    }
  },
  methods: {
    getLoginCount() {
      return parseInt(localStorage.getItem('loginCount') || '1')
    },

    getActiveTime() {
      const hours = parseInt(localStorage.getItem('activeHours') || '24')
      return `${hours}h`
    },

    getDataUsage() {
      const usage = parseFloat(localStorage.getItem('dataUsage') || '1.2')
      return `${usage}GB`
    },

    getAchievements() {
      return parseInt(localStorage.getItem('achievements') || '8')
    },

    formatTime(time) {
      return dayjs(time).fromNow()
    },

    drawActivityChart() {
      const canvas = this.$refs.activityChart
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      const width = canvas.width
      const height = canvas.height

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 模拟数据
      const data = [20, 35, 25, 45, 30, 55, 40]
      const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

      // 设置样式
      ctx.strokeStyle = '#409eff'
      ctx.fillStyle = 'rgba(64, 158, 255, 0.1)'
      ctx.lineWidth = 2

      // 计算坐标
      const padding = 30
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const stepX = chartWidth / (data.length - 1)
      const maxValue = Math.max(...data)

      // 绘制网格线
      ctx.strokeStyle = '#f0f0f0'
      ctx.lineWidth = 1
      for (let i = 0; i <= 4; i++) {
        const y = padding + (chartHeight / 4) * i
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 绘制数据线
      ctx.strokeStyle = '#409eff'
      ctx.lineWidth = 2
      ctx.beginPath()

      data.forEach((value, index) => {
        const x = padding + stepX * index
        const y = padding + chartHeight - (value / maxValue) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()

      // 绘制数据点
      ctx.fillStyle = '#409eff'
      data.forEach((value, index) => {
        const x = padding + stepX * index
        const y = padding + chartHeight - (value / maxValue) * chartHeight

        ctx.beginPath()
        ctx.arc(x, y, 3, 0, Math.PI * 2)
        ctx.fill()
      })
    },

    updateStats() {
      // 更新统计数据
      this.stats.forEach(stat => {
        switch (stat.key) {
          case 'loginCount':
            stat.value = this.getLoginCount()
            break
          case 'activeTime':
            stat.value = this.getActiveTime()
            break
          case 'dataUsage':
            stat.value = this.getDataUsage()
            break
          case 'achievements':
            stat.value = this.getAchievements()
            break
        }
      })
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.drawActivityChart()
    })

    // 定期更新统计数据
    this.updateInterval = setInterval(() => {
      this.updateStats()
    }, 30000) // 30秒更新一次
  },

  beforeUnmount() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }
  }
}
</script>

<style scoped>
.stats-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  height: fit-content;
}

.stats-header {
  margin-bottom: 20px;
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.title-icon {
  color: #409eff;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  margin: 2px 0;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 500;
}

.stat-trend.up {
  color: #67c23a;
}

.stat-trend.down {
  color: #f56c6c;
}

.activity-chart {
  margin-bottom: 25px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.chart-container {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.recent-activities {
  margin-top: 25px;
}

.activities-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.8);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
  line-height: 1.2;
}

.activity-time {
  font-size: 11px;
  color: #7f8c8d;
  margin-top: 2px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
  }
  
  .stat-value {
    font-size: 16px;
  }
}
</style>
