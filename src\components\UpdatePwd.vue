<template>
    <div id="updatepwd">
        <div>
            <span id="updpwd" class="allleft">修改密码</span>
        </div>
        <hr />
        <div>
            <span id="updateuser" class="allleft">个人信息修改：</span>
            <div>
                <!-- 修改头像 -->
                <span class="allleft">修改头像：</span>
                
                <br />
                <span class="allleft">修改昵称：</span>
                <el-input v-model="nickname" style="width: 240px" placeholder="请输入新昵称" clearable />
                <el-button round @click="updateNickname">提交修改</el-button>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'UpdatePwd',
    data() {
        return {
            
        }
    },
    methods: {
        
    },
    mounted() {

    },
}
</script>
<style>
#updatepwd {
    width: 100%;
    height: 100%;
    background-color: rgb(255, 255, 255, 0.5);
    border-radius: 20px;
}

#updpwd {
    text-align: left;
    margin-left: 10px;
    font-size: 20px;
}

.allleft {
    text-align: left;
    margin-left: 10px;
    font-size: 15px;
}
</style>