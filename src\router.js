// 导入路由相关函数
import { createRouter, createWebHashHistory } from "vue-router";
/*
导入以下子组件:
CheckGroup.vue, CheckItem.vue, CheckSet.vue, MyAdmin.vue, CheckBook.vue, CheckData.vue, MyLogin.vue
*/
import UserLogin from "./components/UserLogin.vue"
import HomeIndex from "./components/HomeIndex.vue"
import MyPage from "./components/MyPage.vue";
import ShowMe from "./components/ShowMe.vue";
import PersonalCenter from "./components/PersonalCenter.vue";
import UpdatePwd from "./components/UpdatePwd.vue";

/*创建路由对象数组*/
const routes = [
  {
    path: "/",
    component: UserLogin,
  },
  {
    path: "/admin",
    name: "admin",
    component: HomeIndex,
    children: [
      { path: "MyPage", component: MyPage },
      { path: "ShowMe", component: ShowMe },
      { path: "PersonalCenter", component: PersonalCenter },
      { path: "UpdatePwd", component: UpdatePwd },
    ],
  },
];

/*创建路由实例对象*/
const router = createRouter({
  history: createWebHashHistory(),
  routes: routes,
});

/*导出路由对象 */
export default router;
