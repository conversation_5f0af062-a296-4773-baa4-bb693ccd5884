<template>
    <div id="personalcenter">
        <div>
            <span id="pccenter">个人中心</span>
        </div>
        <hr />
        <div>
            <span id="updateuser" class="allleft">个人信息修改：</span>
            <div>
                <!-- 修改头像 -->
                <span class="allleft">修改头像：</span>
                
                <br />
                <span class="allleft">修改昵称：</span>
                <el-input v-model="nickname" style="width: 240px" placeholder="请输入新昵称" clearable />
                <el-button round @click="updateNickname">提交修改</el-button>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'PersonalCenter',
    data() {
        return {
            nickname: ''
        }
    },
    methods: {
        updateNickname() {
            // sessionStorage.setItem('nickname', this.nickname);
            localStorage.setItem('nickname', this.nickname);
            // 提交后清空输入框
            this.nickname = '';
            // 调用HomeIndex组件的方法，更新昵称
            
            // 修改成功提示
            this.$message({
                message: '修改成功',
                type: 'success'
            });
        }
    },
    mounted() {

    },
}
</script>
<style>
#personalcenter {
    width: 100%;
    height: 100%;
    background-color: rgb(255, 255, 255, 0.5);
    border-radius: 20px;
}

#pccenter {
    text-align: left;
    margin-left: 10px;
    font-size: 20px;
}

.allleft {
    text-align: left;
    margin-left: 10px;
    font-size: 15px;
}
</style>