<template>
  <div class="common-layout">
    <el-container class="app-container">
      <el-header class="app-header">
        <img src="../assets/logo.svg" id="logo" />
        <div id="user" @mouseenter="showDropdown" @mouseleave="hideDropdown">
          <img src="../assets/preview.jpg" id="userimg" />
          <span @click="updateNickname">{{ nickname }}</span>
          <div id="dropdown" v-if="isDropdownVisible">
            <!-- 下拉框内容 -->
            <ul>
              <li><span @click="toPersonalCenter">个人中心</span></li>
              <li><span @click="toUpdatePwd
                ">修改密码</span></li>
              <li><span @click="logout">退出登录</span></li>
            </ul>
          </div>
        </div>
      </el-header>
      <el-container class="main-container">
        <el-aside width="200px" class="app-aside">
          <el-row class="tac" id="aside-tac">
            <el-col :span="24">
              <el-menu default-active="1" class="el-menu-vertical-demo" @open="handleOpen" @close="handleClose">
                <el-sub-menu index="1">
                  <template #title>
                    <el-icon>
                      <location />
                    </el-icon>
                    <span>Navigator One</span>
                  </template>
                  <el-menu-item index="1-1">item one</el-menu-item>
                  <el-menu-item index="1-2">item two</el-menu-item>
                  <el-menu-item index="1-3">item three</el-menu-item>
                </el-sub-menu>
                <el-menu-item index="2">
                  <el-icon><icon-menu /></el-icon>
                  <span>Navigator Two</span>
                </el-menu-item>
                <el-menu-item index="3" @click="toshow">
                  <el-icon>
                    <document />
                  </el-icon>
                  <span>展示</span>
                </el-menu-item>
                <el-menu-item index="4" @click="tosetting">
                  <el-icon>
                    <setting />
                  </el-icon>
                  <span>设置</span>
                </el-menu-item>
              </el-menu>
            </el-col>
          </el-row>
        </el-aside>
        <el-container class="content-container">
          <el-main class="app-main">
            <router-view></router-view>
          </el-main>
          <el-footer class="app-footer">
            <h5>@YJH 2000-3000</h5>
          </el-footer>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'HomeIndex',
  data() {
    return {
      // nickname: sessionStorage.getItem('nickname') || 'testuser',
      nickname: localStorage.getItem('nickname'),
      isDropdownVisible: false, // 控制下拉框显示状态
    };
  },
  methods: {
    tosetting() {
      this.$router.push('/admin/MyPage');
    },
    toshow() {
      this.$router.push('/admin/ShowMe');
    },
    toPersonalCenter() {
      this.$router.push('/admin/PersonalCenter');
    },
    toUpdatePwd() {
      this.$router.push('/admin/UpdatePwd');
    },
    showDropdown() {
      this.isDropdownVisible = true;
    },
    hideDropdown() {
      this.isDropdownVisible = false;
    },
    logout() {
      // 弹出提示框，询问用户是否确认退出
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 确认退出
        // 清空昵称
        // sessionStorage.removeItem('nickname');
        localStorage.removeItem('nickname');
        // 跳转到登录页
        this.$router.push('/');
      }).catch(() => {
        // 取消退出
      });
    },
  },
  mounted() {
    this.tosetting();
  },
};
</script>


<style>
#logo {
  text-align: left;
}

#user {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 150px;
  width: 150px;
  overflow: visible !important;
  cursor: pointer;
}

#userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  margin-right: 5px;
  flex-shrink: 0;
}

#user span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 115px;
}

#dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: rgb(255, 255, 255, 0.5);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  z-index: 9999;
  min-width: 150px;
}

#dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

#dropdown ul li {
  padding: 10px;
  text-align: center; /* 居中内容 */
  height: auto; /* 自动适应内容高度 */
  line-height: 20px; /* 垂直居中对齐 */
}

#dropdown ul li span {
  display: inline-block;
  width: 100%; /* 占据父容器宽度 */
  text-align: center; /* 居中对齐文字 */
  font-size: 14px; /* 调整字体大小 */
  color: #333;
}

#dropdown ul li:hover {
  background-color: rgb(255, 255, 255, 0.3);
}

/* 整个页面布局 */
.common-layout {
  width: 100vw;
  /* 视口宽度 */
  height: 100vh;
  /* 视口高度 */
  overflow: hidden;
  /* 防止出现滚动条，如果你的内容确实超出屏幕，请移除此行 */
}

/* 最外层容器 */
.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.app-header {
  background-color: rgb(255, 255, 255, 0.7);
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 60px;
  flex-shrink: 0;
  padding: 0 20px;
  /* 可选：左右内边距 */
  overflow: visible !important; /* 确保子元素不被裁剪 */
}



#headimg {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

/* 主要内容区 */
.main-container {
  flex: 1;
  /* 占据剩余所有空间 */
  display: flex;
}

/* 侧边栏 */
.app-aside {
  background-color: rgb(255, 255, 255, 0.3);
  color: #333;
  text-align: center;
  line-height: 200px;
  /* 调整Aside高度 */
  flex-shrink: 0;
  /* 确保aside不被压缩 */
}

.app-aside .el-row,
.app-aside .el-col,
.app-aside .el-menu,
.app-aside .el-menu-item {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 内容区容器 */
.content-container {
  flex: 1;
  /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  height: 100%;
  /* 确保容器高度为100% */
}

/* 主要内容 */
.app-main {
  padding: 5px;
  background-color: rgb(255, 255, 255, 0.2);
  color: #333;
  /* 移除 line-height，使内容区域不再固定高度 */
  flex: 1;
  /* 占据内容容器剩余空间 */
  overflow: auto;
  /* 内容超出时显示滚动条 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 如有需要，可以根据需求调整居中方式 */
}


/* 底部 */
.app-footer {
  background-color: rgb(255, 255, 255, 0.5);
  color: #333;
  text-align: left;
  line-height: 20px;
  /* 调整Footer高度 */
  flex-shrink: 0;
  /* 确保footer不被压缩 */
}
</style>