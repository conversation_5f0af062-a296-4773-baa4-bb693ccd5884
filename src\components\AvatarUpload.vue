<template>
  <div class="avatar-upload">
    <div class="avatar-preview" @click="triggerUpload">
      <img 
        :src="avatarUrl" 
        :alt="alt"
        class="avatar-image"
        @error="handleImageError"
      />
      <div class="upload-overlay">
        <el-icon class="upload-icon"><Camera /></el-icon>
        <span class="upload-text">{{ uploadText }}</span>
      </div>
    </div>
    
    <input 
      ref="fileInput"
      type="file"
      accept="image/*"
      @change="handleFileChange"
      style="display: none"
    />
    
    <!-- 裁剪对话框 -->
    <el-dialog 
      v-model="showCropDialog" 
      title="裁剪头像" 
      width="500px"
      :before-close="handleCropDialogClose"
    >
      <div class="crop-container">
        <div class="crop-preview">
          <canvas 
            ref="cropCanvas"
            :width="cropSize"
            :height="cropSize"
            class="crop-canvas"
          ></canvas>
        </div>
        <div class="crop-controls">
          <el-slider 
            v-model="scale"
            :min="0.1"
            :max="3"
            :step="0.1"
            @input="updatePreview"
          />
          <div class="control-buttons">
            <el-button @click="rotateLeft" icon="RefreshLeft">左转</el-button>
            <el-button @click="rotateRight" icon="RefreshRight">右转</el-button>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showCropDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmCrop">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AvatarUpload',
  props: {
    avatarUrl: {
      type: String,
      default: '/src/assets/preview.jpg'
    },
    alt: {
      type: String,
      default: '用户头像'
    },
    uploadText: {
      type: String,
      default: '更换头像'
    },
    size: {
      type: Number,
      default: 120
    },
    cropSize: {
      type: Number,
      default: 300
    },
    maxFileSize: {
      type: Number,
      default: 2 * 1024 * 1024 // 2MB
    }
  },
  emits: ['avatar-change'],
  data() {
    return {
      showCropDialog: false,
      originalImage: null,
      scale: 1,
      rotation: 0,
      offsetX: 0,
      offsetY: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0
    }
  },
  methods: {
    triggerUpload() {
      this.$refs.fileInput.click()
    },

    handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        this.$message.error('请选择图片文件')
        return
      }

      // 验证文件大小
      if (file.size > this.maxFileSize) {
        this.$message.error(`图片大小不能超过 ${this.maxFileSize / 1024 / 1024}MB`)
        return
      }

      this.loadImage(file)
    },

    loadImage(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          this.originalImage = img
          this.resetCropSettings()
          this.showCropDialog = true
          this.$nextTick(() => {
            this.updatePreview()
          })
        }
        img.src = e.target.result
      }
      reader.readAsDataURL(file)
    },

    resetCropSettings() {
      this.scale = 1
      this.rotation = 0
      this.offsetX = 0
      this.offsetY = 0
    },

    updatePreview() {
      if (!this.originalImage) return

      const canvas = this.$refs.cropCanvas
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      const centerX = this.cropSize / 2
      const centerY = this.cropSize / 2

      // 清空画布
      ctx.clearRect(0, 0, this.cropSize, this.cropSize)

      // 保存当前状态
      ctx.save()

      // 移动到中心点
      ctx.translate(centerX + this.offsetX, centerY + this.offsetY)
      
      // 旋转
      ctx.rotate((this.rotation * Math.PI) / 180)
      
      // 缩放
      ctx.scale(this.scale, this.scale)

      // 绘制图片
      const imgWidth = this.originalImage.width
      const imgHeight = this.originalImage.height
      ctx.drawImage(
        this.originalImage,
        -imgWidth / 2,
        -imgHeight / 2,
        imgWidth,
        imgHeight
      )

      // 恢复状态
      ctx.restore()
    },

    rotateLeft() {
      this.rotation -= 90
      this.updatePreview()
    },

    rotateRight() {
      this.rotation += 90
      this.updatePreview()
    },

    confirmCrop() {
      const canvas = this.$refs.cropCanvas
      const croppedDataUrl = canvas.toDataURL('image/jpeg', 0.8)
      
      this.$emit('avatar-change', croppedDataUrl)
      this.showCropDialog = false
      this.$message.success('头像更新成功')
    },

    handleCropDialogClose() {
      this.showCropDialog = false
      this.resetCropSettings()
    },

    handleImageError() {
      this.$emit('avatar-change', '/src/assets/preview.jpg')
    }
  },

  mounted() {
    // 添加鼠标拖拽事件监听
    const canvas = this.$refs.cropCanvas
    if (canvas) {
      canvas.addEventListener('mousedown', this.startDrag)
      canvas.addEventListener('mousemove', this.drag)
      canvas.addEventListener('mouseup', this.endDrag)
      canvas.addEventListener('mouseleave', this.endDrag)
    }
  },

  beforeUnmount() {
    // 移除事件监听
    const canvas = this.$refs.cropCanvas
    if (canvas) {
      canvas.removeEventListener('mousedown', this.startDrag)
      canvas.removeEventListener('mousemove', this.drag)
      canvas.removeEventListener('mouseup', this.endDrag)
      canvas.removeEventListener('mouseleave', this.endDrag)
    }
  }
}
</script>

<style scoped>
.avatar-upload {
  display: inline-block;
}

.avatar-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 4px solid #409eff;
  transition: all 0.3s ease;
}

.avatar-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
}

.avatar-preview:hover .upload-overlay {
  opacity: 1;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.upload-text {
  font-size: 12px;
}

.crop-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.crop-preview {
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.crop-canvas {
  display: block;
  cursor: move;
}

.crop-controls {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
